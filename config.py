import os
import json
import datetime
import requests
from utils import Logger

class Config:
    """配置管理类，负责保存和加载配置信息，使用单例模式确保在整个应用中只有一个实例"""

    _instance = None

    def __new__(cls, *args, **kwargs):
        """创建单例实例"""
        if cls._instance is None:
            cls._instance = super(Config, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self, config_file="config.json"):
        """初始化配置管理器

        Args:
            config_file (str): 配置文件路径
        """
        # 确保只初始化一次
        if self._initialized:
            return

        self.config_file = config_file
        self.config = self._load_config()
        self.server_base_url = "http://qiuov.cn:3339"  # 服务器基础地址

        # 仅在内存中保存的数据
        self._permissions = []  # 权限列表保存在内存中，不写入配置文件

        # 延迟初始化日志器，避免循环导入
        self._logger = None

        self._initialized = True

    def _get_logger(self):
        """获取日志器实例，延迟初始化避免循环导入"""
        if self._logger is None:
            # 使用固定的日志级别，不再从配置文件读取
            log_level_str = "error"
            from utils import LogLevel, Logger
            log_level = LogLevel.from_string(log_level_str)
            self._logger = Logger("Config", log_level)
            self._logger.debug("Config日志器初始化完成")
            self._logger.debug(f"配置文件: {self.config_file}")
            self._logger.debug(f"服务器地址: {self.server_base_url}")
        return self._logger

    @property
    def logger(self):
        """日志器属性"""
        return self._get_logger()

    def _load_config(self):
        """加载配置

        Returns:
            dict: 配置信息
        """
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print("使用默认配置")
        else:
            print("配置文件不存在，创建默认配置")

        # 返回默认配置
        default_config = {
            "log_level": "error",
            "last_groups": [],
            "owner_id": "",
            "novel_search_enabled": True,
            "link_parser_enabled": True,
            "shuqi_enabled": True,
            "zhangxinlei_enabled": True,
            "lofter_enabled": True,
            "key": ""
        }

        # 首次创建配置时，自动保存到文件
        if not os.path.exists(self.config_file):
            self.config = default_config
            # 遍历配置项并保存
            for key, value in default_config.items():
                self.set(key, value)

        return default_config

    def save_config(self):
        """保存所有配置项到文件"""
        # 遍历所有配置项并保存
        for key, value in self.config.items():
            self.set(key, value)


    def get(self, key, default=None):
        """获取配置项

        Args:
            key (str): 配置项键名
            default: 默认值

        Returns:
            配置项值
        """
        return self.config.get(key, default)

    def set(self, key, value):
        """设置配置项

        Args:
            key (str): 配置项键名
            value: 配置项值
        """
        # 先获取完整的配置
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    current_config = json.load(f)
            except:
                # 如果读取失败，使用当前内存中的配置
                current_config = self.config.copy()
        else:
            current_config = self.config.copy()

        # 只更新指定的键值
        current_config[key] = value

        # 更新内存中的配置
        self.config = current_config

        # 保存到文件
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, ensure_ascii=False, indent=4)

    def save_last_groups(self, groups):
        """保存上次选择的群聊

        Args:
            groups (list): 群聊列表
        """
        self.set("last_groups", groups)

    def get_last_groups(self):
        """获取上次选择的群聊

        Returns:
            list: 群聊列表
        """
        return self.get("last_groups", [])

    def set_key(self, key):
        """设置卡密

        Args:
            key (str): 卡密
        """
        self.set("key", key)

    def get_key(self):
        """获取卡密

        Returns:
            str: 卡密
        """
        return self.get("key", "")

    def verify_key(self):
        """验证卡密

        Returns:
            bool: 验证结果
            str: 错误信息或到期时间
        """
        key = self.get_key()
        if not key:
            return False, "未设置卡密"

        # 获取服务器时间
        try:
            server_time_resp = requests.get(f"{self.server_base_url}/api/time.php")
            if server_time_resp.status_code != 200:
                return False, "获取服务器时间失败"
            server_time = server_time_resp.json().get("data")
        except Exception as e:
            print(f"获取服务器时间异常: {e}")
            return False, "获取服务器时间异常"

        # 验证卡密
        try:
            resp = requests.post(f"{self.server_base_url}/api/login.php", data={"key": key})
            if resp.status_code != 200:
                return False, "验证卡密失败"

            # 使用utf-8-sig解码以处理可能的BOM
            try:
                data = json.loads(resp.content.decode('utf-8-sig'))
            except:
                data = resp.json()  # 如果上面的方法失败，尝试默认方法

            if data.get("code") != 200:
                return False, data.get("msg", "验证卡密失败")

            response_data = data.get("data", {})
            expire_time = response_data.get("expires_at", "")
            # 保存权限信息到配置
            self._permissions = response_data.get("permissions", [])
            # 检查是否过期
            expire_date = datetime.datetime.strptime(expire_time, "%Y-%m-%d %H:%M:%S")
            server_date = datetime.datetime.strptime(server_time, "%Y-%m-%d %H:%M:%S")

            if expire_date <= server_date:
                return False, "卡密已过期"

            return True, expire_time
        except Exception as e:
            return False, "验证卡密异常"

    def get_permissions(self):
        """获取当前卡密权限

        Returns:
            list: 权限列表，如 ["找小说", "链接解析"]
        """
        return self._permissions